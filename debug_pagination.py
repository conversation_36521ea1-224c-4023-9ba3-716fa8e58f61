#!/usr/bin/env python3
"""
Debug script to find all article links on a page and understand the structure
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_articles_page():
    """Debug the articles page to find all article links"""
    
    url = "https://ui.charlotte.edu/articles/"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        logger.info(f"Debugging articles page: {url}")
        logger.info(f"Response status: {response.status_code}")
        
        # Method 1: Current approach - links with /202
        all_links = soup.find_all('a', href=True)
        year_links = [link for link in all_links if '/202' in link.get('href', '')]
        
        logger.info(f"\n=== Method 1: Links with '/202' pattern ===")
        logger.info(f"Found {len(year_links)} links with year pattern")
        
        unique_year_articles = {}
        for link in year_links:
            href = link.get('href')
            title = link.get_text(strip=True)
            if href not in unique_year_articles and title.lower() != 'read more':
                unique_year_articles[href] = title
        
        logger.info(f"Unique articles with year pattern: {len(unique_year_articles)}")
        for i, (href, title) in enumerate(unique_year_articles.items(), 1):
            logger.info(f"  {i:2d}. {title[:60]}... -> {href}")
        
        # Method 2: Look for article containers/blocks
        logger.info(f"\n=== Method 2: Article containers ===")
        
        # Try to find article containers
        article_containers = soup.find_all(['article', 'div'], class_=lambda x: x and any(
            keyword in ' '.join(x).lower() for keyword in ['post', 'article', 'entry', 'item']
        ))
        
        logger.info(f"Found {len(article_containers)} potential article containers")
        
        container_articles = set()
        for i, container in enumerate(article_containers[:5]):  # Show first 5
            classes = ' '.join(container.get('class', []))
            logger.info(f"  Container {i+1}: <{container.name} class='{classes}'>")
            
            # Look for links within this container
            container_links = container.find_all('a', href=True)
            for link in container_links:
                href = link.get('href')
                if href and ('ui.charlotte.edu' in href or href.startswith('/')):
                    title = link.get_text(strip=True)
                    if title and len(title) > 10:  # Substantial title
                        full_url = urljoin('https://ui.charlotte.edu', href)
                        container_articles.add((full_url, title))
                        logger.info(f"    -> {title[:50]}... | {full_url}")
        
        # Method 3: Look for specific WordPress/theme patterns
        logger.info(f"\n=== Method 3: WordPress patterns ===")
        
        wp_selectors = [
            '.post',
            '.entry',
            '.article',
            '[class*="post"]',
            '[class*="entry"]',
            '[class*="article"]'
        ]
        
        for selector in wp_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"Selector '{selector}': {len(elements)} elements")
                for i, elem in enumerate(elements[:3]):
                    classes = ' '.join(elem.get('class', []))
                    text = elem.get_text(strip=True)[:100]
                    logger.info(f"  {i+1}. <{elem.name} class='{classes}'> - {text}...")
        
        # Method 4: Look at the page structure more carefully
        logger.info(f"\n=== Method 4: Page structure analysis ===")
        
        # Find the main content area
        main_content = soup.find('main') or soup.find('section', id='main') or soup.find('div', class_='content')
        if main_content:
            logger.info("Found main content area")
            
            # Look for repeated patterns that might be articles
            all_divs = main_content.find_all('div')
            div_classes = {}
            
            for div in all_divs:
                classes = tuple(sorted(div.get('class', [])))
                if classes:
                    div_classes[classes] = div_classes.get(classes, 0) + 1
            
            # Show classes that appear multiple times (likely article containers)
            repeated_classes = {k: v for k, v in div_classes.items() if v > 1}
            logger.info(f"Repeated div classes (potential article containers):")
            for classes, count in sorted(repeated_classes.items(), key=lambda x: x[1], reverse=True)[:10]:
                logger.info(f"  {' '.join(classes)}: {count} occurrences")
        
        return True
        
    except Exception as e:
        logger.error(f"Debug failed: {e}")
        return False

if __name__ == "__main__":
    debug_articles_page()
