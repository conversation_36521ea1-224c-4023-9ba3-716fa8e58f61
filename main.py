import time
import pandas as pd
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import tempfile

# Selenium imports
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC 
from webdriver_manager.chrome import ChromeDriverManager
from selenium_stealth import stealth

BASE_URL = "https://ui.charlotte.edu"
ARTICLES_PAGE_URL = f"{BASE_URL}/articles/"

def get_all_article_links_with_selenium(start_url):
    print("--- Initializing STEALTH browser to collect article links ---")
    
    options = webdriver.ChromeOptions()
    # options.add_argument("--headless") 
    options.add_argument("--window-size=1920,1080")
    options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    user_data_dir = tempfile.mkdtemp()
    options.add_argument(f"--user-data-dir={user_data_dir}")

    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)

    stealth(driver,
            languages=["en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True,
            )

    article_urls = []
    current_url = start_url

    try:
        while current_url:
            print(f"Stealth browser navigating to: {current_url}")
            driver.get(current_url)
            
            # *** THE ONLY CHANGE IS ON THE NEXT LINE ***
            # We are now waiting for the element to be clickable, not just present.
            # I've also increased the timeout to 20 seconds just in case.
            WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "h2.entry-title a"))
            )

            links_on_page = driver.find_elements(By.CSS_SELECTOR, "h2.entry-title a")
            
            for link in links_on_page:
                article_urls.append(link.get_attribute('href'))

            try:
                next_page_element = driver.find_element(By.CSS_SELECTOR, "a.next.page-numbers")
                current_url = next_page_element.get_attribute('href')
            except:
                print("  [!] No 'Next' page link found. Reached the last page.")
                current_url = None

    finally:
        driver.quit()

    print(f"--- Found a total of {len(article_urls)} unique articles ---\n")
    return list(set(article_urls))

def scrape_article_details(article_url):
    links_data, images_data, references_data = [], [], []

    try:
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
        response = requests.get(article_url, headers=headers, timeout=15)
        if response.status_code != 200: return [], [], []
        
        soup = BeautifulSoup(response.content, 'lxml')
        content_area = soup.find('div', class_='entry-content')
        if not content_area: return [], [], []

        for link in content_area.find_all('a', href=True):
            links_data.append({'Article URL': article_url, 'Link Name': link.get_text(strip=True), 'Link URL': urljoin(BASE_URL, link['href'])})
        for image in content_area.find_all('img', src=True):
            images_data.append({'Article URL': article_url, 'Image URL': urljoin(BASE_URL, image['src'])})
        
        references_heading = content_area.find(lambda tag: tag.name in ['strong', 'h3', 'h2'] and 'references' in tag.get_text(strip=True).lower())
        if references_heading:
            reference_list = references_heading.find_next_sibling('ul')
            if reference_list:
                for item in reference_list.find_all('li'):
                    ref_link_tag = item.find('a', href=True)
                    references_data.append({'Article URL': article_url, 'Reference Text': item.get_text(strip=True), 'Link URL': urljoin(BASE_URL, ref_link_tag['href']) if ref_link_tag else 'No Link Found'})

    except requests.exceptions.RequestException as e:
        print(f"  [!] Could not scrape {article_url} with requests: {e}")

    return links_data, images_data, references_data

# --- Main execution block ---
if __name__ == "__main__":
    article_urls_to_scrape = get_all_article_links_with_selenium(ARTICLES_PAGE_URL)

    if not article_urls_to_scrape:
        print("\nNo articles were found. The script will now exit.")
    else:
        all_links, all_images, all_references = [], [], []
        total_articles = len(article_urls_to_scrape)
        
        for i, url in enumerate(article_urls_to_scrape):
            print(f"Scraping article {i+1}/{total_articles}...")
            links, images, references = scrape_article_details(url)
            all_links.extend(links)
            all_images.extend(images)
            all_references.extend(references)
            time.sleep(0.25)

        print("\n--- Scraping complete. Creating CSV files. ---")
        pd.DataFrame(all_links).to_csv('links.csv', index=False)
        pd.DataFrame(all_images).to_csv('images.csv', index=False)
        pd.DataFrame(all_references).to_csv('references.csv', index=False)

        print("\n✅ Success! The following files have been created:")
        print(f"- links.csv ({len(all_links)} rows)")
        print(f"- images.csv ({len(all_images)} rows)")
        print(f"- references.csv ({len(all_references)} rows)")