import time
import pandas as pd
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
import random

# Configuration
BASE_URL = "https://ui.charlotte.edu"
ARTICLES_PAGE_URL = f"{BASE_URL}/articles/"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# User agents for rotation
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0'
]

def make_request(url):
    """Make a request with random user agent and delay"""
    headers = {'User-Agent': random.choice(USER_AGENTS)}
    time.sleep(random.uniform(1.0, 3.0))  # Random delay

    response = requests.get(url, headers=headers, timeout=30)
    response.raise_for_status()
    return response

def get_all_article_urls():
    """Get all article URLs from all pages"""
    logger.info("Starting article URL collection...")

    article_urls = set()
    current_url = ARTICLES_PAGE_URL
    page_count = 0

    while current_url and page_count < 200:  # Safety limit
        page_count += 1
        logger.info(f"Scraping page {page_count}: {current_url}")

        try:
            response = make_request(current_url)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find article links using container structure
            article_containers = soup.find_all('div', class_='listing-item-title')
            page_articles = set()

            for container in article_containers:
                link = container.find('a', href=True)
                if link:
                    href = link.get('href', '')
                    title = link.get_text(strip=True)

                    if href and title:
                        full_url = urljoin(BASE_URL, href)
                        if full_url not in page_articles:
                            article_urls.add(full_url)
                            page_articles.add(full_url)

            if not page_articles:
                logger.warning(f"No article links found on page {page_count}")
                break

            logger.info(f"Found {len(page_articles)} articles on page {page_count}")

            # Find next page link
            next_link = soup.select_one('a.next.page-numbers')
            if next_link and next_link.get('href'):
                current_url = urljoin(BASE_URL, next_link['href'])
            else:
                logger.info("No more pages found")
                break

        except Exception as e:
            logger.error(f"Error scraping page {page_count}: {e}")
            break

    logger.info(f"Found {len(article_urls)} total articles")
    return list(article_urls)

def scrape_article_content(article_url):
    """Scrape links, images, and references from an article"""
    logger.debug(f"Scraping article: {article_url}")

    try:
        response = make_request(article_url)
        soup = BeautifulSoup(response.content, 'html.parser')

        # Find the main content area
        content_area = soup.find('div', class_='page-body')
        if not content_area:
            content_area = soup.find('section', id='main') or soup.find('div', class_='page')
            if not content_area:
                logger.warning(f"No content area found for {article_url}")
                return [], [], []

        # Extract links
        links_data = []
        for link in content_area.find_all('a', href=True):
            href = link.get('href', '').strip()
            if href and not href.startswith('#') and not href.startswith('javascript:'):
                link_text = link.get_text(strip=True) or href
                full_url = urljoin(BASE_URL, href)
                links_data.append({
                    'Article URL': article_url,
                    'Link Name': link_text,
                    'Link URL': full_url
                })

        # Extract images
        images_data = []
        for img in content_area.find_all('img'):
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if src:
                full_url = urljoin(BASE_URL, src)
                images_data.append({
                    'Article URL': article_url,
                    'Image URL': full_url
                })

        # Extract references (simplified)
        references_data = []
        text_content = content_area.get_text()
        # Look for inline references like [2023], (2023), etc.
        import re
        inline_refs = re.findall(r'\[(\d{4})\]|\((\d{4})\)', text_content)
        for match in inline_refs:
            year = match[0] or match[1]
            references_data.append({
                'Article URL': article_url,
                'Reference Text': f'Inline reference {year}',
                'Link URL': 'No Link Found'
            })

        logger.debug(f"Extracted {len(links_data)} links, {len(images_data)} images, {len(references_data)} references")
        return links_data, images_data, references_data

    except Exception as e:
        logger.error(f"Error scraping {article_url}: {e}")
        return [], [], []

    def _get_urls_with_selenium(self) -> List[str]:
        """Fallback method using Selenium with enhanced stealth"""
        logger.info("Initializing Selenium browser for URL collection...")

        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--window-size=1920,1080")

        # Random user agent
        user_agent = random.choice(self.config.user_agents)
        options.add_argument(f"user-agent={user_agent}")

        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)

        try:
            stealth(driver,
                    languages=["en-US", "en"],
                    vendor="Google Inc.",
                    platform="Win32",
                    webgl_vendor="Intel Inc.",
                    renderer="Intel Iris OpenGL Engine",
                    fix_hairline=True)

            article_urls = set()
            current_url = self.config.articles_url
            page_count = 0

            while current_url and page_count < 200:  # Safety limit
                page_count += 1
                logger.info(f"Selenium scraping page {page_count}: {current_url}")

                try:
                    driver.get(current_url)

                    # Wait for content to load
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "h2.entry-title a"))
                    )

                    # Random delay to appear more human
                    time.sleep(random.uniform(2, 5))

                    # Get article links using the container structure
                    try:
                        article_containers = driver.find_elements(By.CSS_SELECTOR, "div.listing-item-title")
                        page_articles = set()

                        for container in article_containers:
                            try:
                                link = container.find_element(By.TAG_NAME, "a")
                                href = link.get_attribute('href')
                                title = link.text.strip()

                                if href and title:
                                    if href not in page_articles:
                                        article_urls.add(href)
                                        page_articles.add(href)
                            except:
                                continue

                        # Fallback to year pattern if no containers found
                        if not page_articles:
                            all_links = driver.find_elements(By.TAG_NAME, "a")
                            for link in all_links:
                                href = link.get_attribute('href')
                                if href and '/202' in href:
                                    title = link.text.strip()
                                    if title.lower() != 'read more' and title:
                                        if href not in page_articles:
                                            article_urls.add(href)
                                            page_articles.add(href)

                        logger.info(f"Found {len(page_articles)} articles on page {page_count}")

                    except Exception as e:
                        logger.error(f"Error extracting articles on page {page_count}: {e}")
                        break

                    # Find next page
                    try:
                        next_button = driver.find_element(By.CSS_SELECTOR, "a.next.page-numbers")
                        current_url = next_button.get_attribute('href')
                    except:
                        logger.info("No more pages found")
                        break

                except TimeoutException:
                    logger.error(f"Timeout waiting for page {page_count} to load")
                    break
                except Exception as e:
                    logger.error(f"Error on page {page_count}: {e}")
                    break

            return list(article_urls)

        finally:
            driver.quit()

    def close(self):
        """Clean up resources"""
        self.session.close()

class ArticleContentScraper:
    """Scrapes content from individual article pages"""

    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.session = RobustSession(config)

    def scrape_article_details(self, article_url: str) -> Tuple[List[LinkData], List[ImageData], List[ReferenceData]]:
        """Scrape links, images, and references from an article"""
        logger.debug(f"Scraping article: {article_url}")

        try:
            response = self.session.get(article_url)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find the main content area using the selector we discovered
            content_area = soup.find('div', class_='page-body')
            if not content_area:
                # Fallback selectors
                content_area = soup.find('section', id='main') or soup.find('div', class_='page')
                if not content_area:
                    logger.warning(f"No content area found for {article_url}")
                    return [], [], []

            links_data = self._extract_links(article_url, content_area)
            images_data = self._extract_images(article_url, content_area)
            references_data = self._extract_references(article_url, content_area)

            logger.debug(f"Extracted {len(links_data)} links, {len(images_data)} images, {len(references_data)} references")
            return links_data, images_data, references_data

        except Exception as e:
            logger.error(f"Error scraping {article_url}: {e}")
            return [], [], []

    def _extract_links(self, article_url: str, content_area) -> List[LinkData]:
        """Extract all links from the content area"""
        links_data = []

        for link in content_area.find_all('a', href=True):
            href = link.get('href', '').strip()
            if not href:
                continue

            # Skip anchor links and javascript
            if href.startswith('#') or href.startswith('javascript:'):
                continue

            link_text = link.get_text(strip=True)
            if not link_text:
                link_text = href  # Use URL as text if no text available

            # Convert relative URLs to absolute
            full_url = urljoin(self.config.base_url, href)

            links_data.append(LinkData(
                article_url=article_url,
                link_name=link_text,
                link_url=full_url
            ))

        return links_data

    def _extract_images(self, article_url: str, content_area) -> List[ImageData]:
        """Extract all images from the content area"""
        images_data = []

        for img in content_area.find_all('img'):
            # Try different src attributes
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if not src:
                continue

            # Convert relative URLs to absolute
            full_url = urljoin(self.config.base_url, src)

            images_data.append(ImageData(
                article_url=article_url,
                image_url=full_url
            ))

        return images_data

    def _extract_references(self, article_url: str, content_area) -> List[ReferenceData]:
        """Extract references/citations from the content area"""
        references_data = []

        # Look for references section
        reference_patterns = [
            r'references?',
            r'citations?',
            r'sources?',
            r'bibliography',
            r'works cited'
        ]

        for pattern in reference_patterns:
            # Look for headings containing reference keywords
            headings = content_area.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'b'])

            for heading in headings:
                heading_text = heading.get_text(strip=True).lower()
                if re.search(pattern, heading_text):
                    references_data.extend(self._extract_references_from_section(article_url, heading))
                    break

        # Also look for numbered references in the text
        references_data.extend(self._extract_inline_references(article_url, content_area))

        return references_data

    def _extract_references_from_section(self, article_url: str, heading) -> List[ReferenceData]:
        """Extract references from a references section"""
        references = []

        # Look for list after the heading
        next_element = heading.find_next_sibling()
        while next_element:
            if next_element.name in ['ul', 'ol']:
                for item in next_element.find_all('li'):
                    ref_text = item.get_text(strip=True)
                    if ref_text:
                        # Look for links in the reference
                        ref_link = item.find('a', href=True)
                        link_url = urljoin(self.config.base_url, ref_link['href']) if ref_link else 'No Link Found'

                        references.append(ReferenceData(
                            article_url=article_url,
                            reference_text=ref_text,
                            link_url=link_url
                        ))
                break
            elif next_element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                # Stop if we hit another heading
                break
            next_element = next_element.find_next_sibling()

        return references

    def _extract_inline_references(self, article_url: str, content_area) -> List[ReferenceData]:
        """Extract inline references (like [1], [2], etc.)"""
        references = []

        # Look for patterns like [1], (1), etc.
        text = content_area.get_text()
        inline_ref_pattern = r'\[(\d+)\]|\((\d+)\)'

        matches = re.finditer(inline_ref_pattern, text)
        for match in matches:
            ref_num = match.group(1) or match.group(2)
            # This is a simplified extraction - in practice, you'd need to
            # find the actual reference text that corresponds to this number
            references.append(ReferenceData(
                article_url=article_url,
                reference_text=f"Inline reference {ref_num}",
                link_url='No Link Found'
            ))

        return references

    def close(self):
        """Clean up resources"""
        self.session.close()

class DataManager:
    """Manages data collection and export"""

    def __init__(self):
        self.links_data: List[LinkData] = []
        self.images_data: List[ImageData] = []
        self.references_data: List[ReferenceData] = []

    def add_article_data(self, links: List[LinkData], images: List[ImageData], references: List[ReferenceData]):
        """Add data from a single article"""
        self.links_data.extend(links)
        self.images_data.extend(images)
        self.references_data.extend(references)

    def export_to_csv(self, links_file: str = 'links.csv',
                     images_file: str = 'images.csv',
                     references_file: str = 'references.csv'):
        """Export all data to CSV files"""

        # Convert dataclasses to dictionaries for pandas
        links_dict = [
            {
                'Article URL': link.article_url,
                'Link Name': link.link_name,
                'Link URL': link.link_url
            }
            for link in self.links_data
        ]

        images_dict = [
            {
                'Article URL': image.article_url,
                'Image URL': image.image_url
            }
            for image in self.images_data
        ]

        references_dict = [
            {
                'Article URL': ref.article_url,
                'Reference Text': ref.reference_text,
                'Link URL': ref.link_url
            }
            for ref in self.references_data
        ]

        # Create DataFrames and export
        pd.DataFrame(links_dict).to_csv(links_file, index=False)
        pd.DataFrame(images_dict).to_csv(images_file, index=False)
        pd.DataFrame(references_dict).to_csv(references_file, index=False)

        logger.info(f"Exported {len(self.links_data)} links to {links_file}")
        logger.info(f"Exported {len(self.images_data)} images to {images_file}")
        logger.info(f"Exported {len(self.references_data)} references to {references_file}")

    def get_summary(self) -> Dict[str, int]:
        """Get summary statistics"""
        return {
            'total_links': len(self.links_data),
            'total_images': len(self.images_data),
            'total_references': len(self.references_data),
            'unique_articles': len(set(link.article_url for link in self.links_data))
        }

class UIArticlesScraper:
    """Main scraper class that orchestrates the entire process"""

    def __init__(self, config: Optional[ScrapingConfig] = None):
        self.config = config or ScrapingConfig()
        self.url_collector = ArticleURLCollector(self.config)
        self.content_scraper = ArticleContentScraper(self.config)
        self.data_manager = DataManager()

    def run_scraping_process(self) -> Dict[str, int]:
        """Run the complete scraping process"""
        start_time = datetime.now()
        logger.info("Starting UI Charlotte articles scraping process...")

        try:
            # Step 1: Collect all article URLs
            logger.info("Step 1: Collecting article URLs...")
            article_urls = self.url_collector.get_all_article_urls()

            if not article_urls:
                logger.error("No article URLs found. Exiting.")
                return {}

            logger.info(f"Found {len(article_urls)} articles to scrape")

            # Step 2: Scrape each article
            logger.info("Step 2: Scraping article content...")
            failed_articles = []

            for i, url in enumerate(article_urls, 1):
                logger.info(f"Scraping article {i}/{len(article_urls)}: {url}")

                try:
                    links, images, references = self.content_scraper.scrape_article_details(url)
                    self.data_manager.add_article_data(links, images, references)

                    # Log progress every 10 articles
                    if i % 10 == 0:
                        summary = self.data_manager.get_summary()
                        logger.info(f"Progress: {i}/{len(article_urls)} articles processed. "
                                  f"Total: {summary['total_links']} links, "
                                  f"{summary['total_images']} images, "
                                  f"{summary['total_references']} references")

                except Exception as e:
                    logger.error(f"Failed to scrape article {url}: {e}")
                    failed_articles.append(url)

            # Step 3: Export data
            logger.info("Step 3: Exporting data to CSV files...")
            self.data_manager.export_to_csv()

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time
            summary = self.data_manager.get_summary()

            logger.info("Scraping process completed!")
            logger.info(f"Duration: {duration}")
            logger.info(f"Articles processed: {len(article_urls) - len(failed_articles)}/{len(article_urls)}")
            logger.info(f"Failed articles: {len(failed_articles)}")
            logger.info(f"Total links extracted: {summary['total_links']}")
            logger.info(f"Total images extracted: {summary['total_images']}")
            logger.info(f"Total references extracted: {summary['total_references']}")

            if failed_articles:
                logger.warning("Failed articles:")
                for url in failed_articles:
                    logger.warning(f"  - {url}")

            return summary

        except Exception as e:
            logger.error(f"Scraping process failed: {e}")
            raise
        finally:
            self.cleanup()

    def cleanup(self):
        """Clean up resources"""
        try:
            self.url_collector.close()
            self.content_scraper.close()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# --- Main execution block ---
def main():
    """Main function to run the scraping process"""
    try:
        # Create configuration
        config = ScrapingConfig()

        # Create and run scraper
        scraper = UIArticlesScraper(config)
        summary = scraper.run_scraping_process()

        if summary:
            print("\n✅ Scraping completed successfully!")
            print(f"📊 Summary:")
            print(f"   - Total links: {summary['total_links']}")
            print(f"   - Total images: {summary['total_images']}")
            print(f"   - Total references: {summary['total_references']}")
            print(f"   - Articles processed: {summary['unique_articles']}")
            print(f"\n📁 Files created:")
            print(f"   - links.csv")
            print(f"   - images.csv")
            print(f"   - references.csv")
            print(f"   - scraper.log")
        else:
            print("❌ Scraping failed. Check scraper.log for details.")

    except Exception as e:
        logger.error(f"Main execution failed: {e}")
        print(f"❌ Error: {e}")
        print("Check scraper.log for detailed error information.")

if __name__ == "__main__":
    main()