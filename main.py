import time
import pandas as pd
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import tempfile
import logging
import random
import json
import os
from datetime import datetime
import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Selenium imports
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium_stealth import stealth

# Configuration
BASE_URL = "https://ui.charlotte.edu"
ARTICLES_PAGE_URL = f"{BASE_URL}/articles/"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScrapingConfig:
    """Configuration for the scraping process"""
    base_url: str = BASE_URL
    articles_url: str = ARTICLES_PAGE_URL
    request_delay_min: float = 1.0
    request_delay_max: float = 3.0
    max_retries: int = 3
    timeout: int = 30
    user_agents: List[str] = None

    def __post_init__(self):
        if self.user_agents is None:
            self.user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
            ]

@dataclass
class LinkData:
    """Data structure for link information"""
    article_url: str
    link_name: str
    link_url: str

@dataclass
class ImageData:
    """Data structure for image information"""
    article_url: str
    image_url: str

@dataclass
class ReferenceData:
    """Data structure for reference information"""
    article_url: str
    reference_text: str
    link_url: str

class RobustSession:
    """Enhanced session manager with retry logic and anti-detection measures"""

    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.session = requests.Session()
        self._setup_session()

    def _setup_session(self):
        """Configure session with retry strategy and headers"""
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Set default headers
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def get(self, url: str, **kwargs) -> requests.Response:
        """Make a GET request with random user agent and delay"""
        # Random user agent
        self.session.headers['User-Agent'] = random.choice(self.config.user_agents)

        # Random delay
        delay = random.uniform(self.config.request_delay_min, self.config.request_delay_max)
        time.sleep(delay)

        # Set timeout
        kwargs.setdefault('timeout', self.config.timeout)

        logger.debug(f"Making request to: {url}")
        response = self.session.get(url, **kwargs)
        response.raise_for_status()

        return response

    def close(self):
        """Close the session"""
        self.session.close()

class ArticleURLCollector:
    """Collects article URLs using multiple strategies for robustness"""

    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.session = RobustSession(config)

    def get_all_article_urls(self) -> List[str]:
        """Get all article URLs using the most reliable method available"""
        logger.info("Starting article URL collection...")

        # Try requests-based approach first (faster and less detectable)
        try:
            urls = self._get_urls_with_requests()
            if urls:
                logger.info(f"Successfully collected {len(urls)} URLs using requests")
                return urls
        except Exception as e:
            logger.warning(f"Requests-based collection failed: {e}")

        # Fallback to Selenium if requests fails
        try:
            urls = self._get_urls_with_selenium()
            logger.info(f"Successfully collected {len(urls)} URLs using Selenium")
            return urls
        except Exception as e:
            logger.error(f"Selenium-based collection failed: {e}")
            raise Exception("All URL collection methods failed")

    def _get_urls_with_requests(self) -> List[str]:
        """Collect URLs using requests and BeautifulSoup"""
        article_urls = set()
        current_url = self.config.articles_url
        page_count = 0

        while current_url and page_count < 200:  # Safety limit
            page_count += 1
            logger.info(f"Scraping page {page_count}: {current_url}")

            try:
                response = self.session.get(current_url)
                soup = BeautifulSoup(response.content, 'html.parser')

                # Find article links using the pattern we discovered
                all_links = soup.find_all('a', href=True)
                page_articles = set()

                for link in all_links:
                    href = link.get('href', '')
                    # Look for links with year patterns (2020-2029)
                    if '/202' in href and href not in page_articles:
                        title = link.get_text(strip=True)
                        # Skip "Read more" links and empty titles
                        if title.lower() != 'read more' and title:
                            full_url = urljoin(self.config.base_url, href)
                            article_urls.add(full_url)
                            page_articles.add(href)

                if not page_articles:
                    logger.warning(f"No article links found on page {page_count}")
                    break

                logger.info(f"Found {len(page_articles)} articles on page {page_count}")

                # Find next page link
                next_link = soup.select_one('a.next.page-numbers')
                if next_link and next_link.get('href'):
                    current_url = urljoin(self.config.base_url, next_link['href'])
                else:
                    logger.info("No more pages found")
                    break

            except Exception as e:
                logger.error(f"Error scraping page {page_count}: {e}")
                break

        return list(article_urls)

    def _get_urls_with_selenium(self) -> List[str]:
        """Fallback method using Selenium with enhanced stealth"""
        logger.info("Initializing Selenium browser for URL collection...")

        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--window-size=1920,1080")

        # Random user agent
        user_agent = random.choice(self.config.user_agents)
        options.add_argument(f"user-agent={user_agent}")

        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)

        try:
            stealth(driver,
                    languages=["en-US", "en"],
                    vendor="Google Inc.",
                    platform="Win32",
                    webgl_vendor="Intel Inc.",
                    renderer="Intel Iris OpenGL Engine",
                    fix_hairline=True)

            article_urls = set()
            current_url = self.config.articles_url
            page_count = 0

            while current_url and page_count < 200:  # Safety limit
                page_count += 1
                logger.info(f"Selenium scraping page {page_count}: {current_url}")

                try:
                    driver.get(current_url)

                    # Wait for content to load
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "h2.entry-title a"))
                    )

                    # Random delay to appear more human
                    time.sleep(random.uniform(2, 5))

                    # Get article links using the pattern we discovered
                    all_links = driver.find_elements(By.TAG_NAME, "a")
                    page_articles = set()

                    for link in all_links:
                        href = link.get_attribute('href')
                        if href and '/202' in href:
                            title = link.text.strip()
                            if title.lower() != 'read more' and title:
                                article_urls.add(href)
                                page_articles.add(href)

                    logger.info(f"Found {len(page_articles)} articles on page {page_count}")

                    # Find next page
                    try:
                        next_button = driver.find_element(By.CSS_SELECTOR, "a.next.page-numbers")
                        current_url = next_button.get_attribute('href')
                    except:
                        logger.info("No more pages found")
                        break

                except TimeoutException:
                    logger.error(f"Timeout waiting for page {page_count} to load")
                    break
                except Exception as e:
                    logger.error(f"Error on page {page_count}: {e}")
                    break

            return list(article_urls)

        finally:
            driver.quit()

    def close(self):
        """Clean up resources"""
        self.session.close()

class ArticleContentScraper:
    """Scrapes content from individual article pages"""

    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.session = RobustSession(config)

    def scrape_article_details(self, article_url: str) -> Tuple[List[LinkData], List[ImageData], List[ReferenceData]]:
        """Scrape links, images, and references from an article"""
        logger.debug(f"Scraping article: {article_url}")

        try:
            response = self.session.get(article_url)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find the main content area using the selector we discovered
            content_area = soup.find('div', class_='page-body')
            if not content_area:
                # Fallback selectors
                content_area = soup.find('section', id='main') or soup.find('div', class_='page')
                if not content_area:
                    logger.warning(f"No content area found for {article_url}")
                    return [], [], []

            links_data = self._extract_links(article_url, content_area)
            images_data = self._extract_images(article_url, content_area)
            references_data = self._extract_references(article_url, content_area)

            logger.debug(f"Extracted {len(links_data)} links, {len(images_data)} images, {len(references_data)} references")
            return links_data, images_data, references_data

        except Exception as e:
            logger.error(f"Error scraping {article_url}: {e}")
            return [], [], []

    def _extract_links(self, article_url: str, content_area) -> List[LinkData]:
        """Extract all links from the content area"""
        links_data = []

        for link in content_area.find_all('a', href=True):
            href = link.get('href', '').strip()
            if not href:
                continue

            # Skip anchor links and javascript
            if href.startswith('#') or href.startswith('javascript:'):
                continue

            link_text = link.get_text(strip=True)
            if not link_text:
                link_text = href  # Use URL as text if no text available

            # Convert relative URLs to absolute
            full_url = urljoin(self.config.base_url, href)

            links_data.append(LinkData(
                article_url=article_url,
                link_name=link_text,
                link_url=full_url
            ))

        return links_data

    def _extract_images(self, article_url: str, content_area) -> List[ImageData]:
        """Extract all images from the content area"""
        images_data = []

        for img in content_area.find_all('img'):
            # Try different src attributes
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if not src:
                continue

            # Convert relative URLs to absolute
            full_url = urljoin(self.config.base_url, src)

            images_data.append(ImageData(
                article_url=article_url,
                image_url=full_url
            ))

        return images_data

    def _extract_references(self, article_url: str, content_area) -> List[ReferenceData]:
        """Extract references/citations from the content area"""
        references_data = []

        # Look for references section
        reference_patterns = [
            r'references?',
            r'citations?',
            r'sources?',
            r'bibliography',
            r'works cited'
        ]

        for pattern in reference_patterns:
            # Look for headings containing reference keywords
            headings = content_area.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'b'])

            for heading in headings:
                heading_text = heading.get_text(strip=True).lower()
                if re.search(pattern, heading_text):
                    references_data.extend(self._extract_references_from_section(article_url, heading))
                    break

        # Also look for numbered references in the text
        references_data.extend(self._extract_inline_references(article_url, content_area))

        return references_data

    def _extract_references_from_section(self, article_url: str, heading) -> List[ReferenceData]:
        """Extract references from a references section"""
        references = []

        # Look for list after the heading
        next_element = heading.find_next_sibling()
        while next_element:
            if next_element.name in ['ul', 'ol']:
                for item in next_element.find_all('li'):
                    ref_text = item.get_text(strip=True)
                    if ref_text:
                        # Look for links in the reference
                        ref_link = item.find('a', href=True)
                        link_url = urljoin(self.config.base_url, ref_link['href']) if ref_link else 'No Link Found'

                        references.append(ReferenceData(
                            article_url=article_url,
                            reference_text=ref_text,
                            link_url=link_url
                        ))
                break
            elif next_element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                # Stop if we hit another heading
                break
            next_element = next_element.find_next_sibling()

        return references

    def _extract_inline_references(self, article_url: str, content_area) -> List[ReferenceData]:
        """Extract inline references (like [1], [2], etc.)"""
        references = []

        # Look for patterns like [1], (1), etc.
        text = content_area.get_text()
        inline_ref_pattern = r'\[(\d+)\]|\((\d+)\)'

        matches = re.finditer(inline_ref_pattern, text)
        for match in matches:
            ref_num = match.group(1) or match.group(2)
            # This is a simplified extraction - in practice, you'd need to
            # find the actual reference text that corresponds to this number
            references.append(ReferenceData(
                article_url=article_url,
                reference_text=f"Inline reference {ref_num}",
                link_url='No Link Found'
            ))

        return references

    def close(self):
        """Clean up resources"""
        self.session.close()

class DataManager:
    """Manages data collection and export"""

    def __init__(self):
        self.links_data: List[LinkData] = []
        self.images_data: List[ImageData] = []
        self.references_data: List[ReferenceData] = []

    def add_article_data(self, links: List[LinkData], images: List[ImageData], references: List[ReferenceData]):
        """Add data from a single article"""
        self.links_data.extend(links)
        self.images_data.extend(images)
        self.references_data.extend(references)

    def export_to_csv(self, links_file: str = 'links.csv',
                     images_file: str = 'images.csv',
                     references_file: str = 'references.csv'):
        """Export all data to CSV files"""

        # Convert dataclasses to dictionaries for pandas
        links_dict = [
            {
                'Article URL': link.article_url,
                'Link Name': link.link_name,
                'Link URL': link.link_url
            }
            for link in self.links_data
        ]

        images_dict = [
            {
                'Article URL': image.article_url,
                'Image URL': image.image_url
            }
            for image in self.images_data
        ]

        references_dict = [
            {
                'Article URL': ref.article_url,
                'Reference Text': ref.reference_text,
                'Link URL': ref.link_url
            }
            for ref in self.references_data
        ]

        # Create DataFrames and export
        pd.DataFrame(links_dict).to_csv(links_file, index=False)
        pd.DataFrame(images_dict).to_csv(images_file, index=False)
        pd.DataFrame(references_dict).to_csv(references_file, index=False)

        logger.info(f"Exported {len(self.links_data)} links to {links_file}")
        logger.info(f"Exported {len(self.images_data)} images to {images_file}")
        logger.info(f"Exported {len(self.references_data)} references to {references_file}")

    def get_summary(self) -> Dict[str, int]:
        """Get summary statistics"""
        return {
            'total_links': len(self.links_data),
            'total_images': len(self.images_data),
            'total_references': len(self.references_data),
            'unique_articles': len(set(link.article_url for link in self.links_data))
        }

class UIArticlesScraper:
    """Main scraper class that orchestrates the entire process"""

    def __init__(self, config: Optional[ScrapingConfig] = None):
        self.config = config or ScrapingConfig()
        self.url_collector = ArticleURLCollector(self.config)
        self.content_scraper = ArticleContentScraper(self.config)
        self.data_manager = DataManager()

    def run_scraping_process(self) -> Dict[str, int]:
        """Run the complete scraping process"""
        start_time = datetime.now()
        logger.info("Starting UI Charlotte articles scraping process...")

        try:
            # Step 1: Collect all article URLs
            logger.info("Step 1: Collecting article URLs...")
            article_urls = self.url_collector.get_all_article_urls()

            if not article_urls:
                logger.error("No article URLs found. Exiting.")
                return {}

            logger.info(f"Found {len(article_urls)} articles to scrape")

            # Step 2: Scrape each article
            logger.info("Step 2: Scraping article content...")
            failed_articles = []

            for i, url in enumerate(article_urls, 1):
                logger.info(f"Scraping article {i}/{len(article_urls)}: {url}")

                try:
                    links, images, references = self.content_scraper.scrape_article_details(url)
                    self.data_manager.add_article_data(links, images, references)

                    # Log progress every 10 articles
                    if i % 10 == 0:
                        summary = self.data_manager.get_summary()
                        logger.info(f"Progress: {i}/{len(article_urls)} articles processed. "
                                  f"Total: {summary['total_links']} links, "
                                  f"{summary['total_images']} images, "
                                  f"{summary['total_references']} references")

                except Exception as e:
                    logger.error(f"Failed to scrape article {url}: {e}")
                    failed_articles.append(url)

            # Step 3: Export data
            logger.info("Step 3: Exporting data to CSV files...")
            self.data_manager.export_to_csv()

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time
            summary = self.data_manager.get_summary()

            logger.info("Scraping process completed!")
            logger.info(f"Duration: {duration}")
            logger.info(f"Articles processed: {len(article_urls) - len(failed_articles)}/{len(article_urls)}")
            logger.info(f"Failed articles: {len(failed_articles)}")
            logger.info(f"Total links extracted: {summary['total_links']}")
            logger.info(f"Total images extracted: {summary['total_images']}")
            logger.info(f"Total references extracted: {summary['total_references']}")

            if failed_articles:
                logger.warning("Failed articles:")
                for url in failed_articles:
                    logger.warning(f"  - {url}")

            return summary

        except Exception as e:
            logger.error(f"Scraping process failed: {e}")
            raise
        finally:
            self.cleanup()

    def cleanup(self):
        """Clean up resources"""
        try:
            self.url_collector.close()
            self.content_scraper.close()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# --- Main execution block ---
def main():
    """Main function to run the scraping process"""
    try:
        # Create configuration
        config = ScrapingConfig()

        # Create and run scraper
        scraper = UIArticlesScraper(config)
        summary = scraper.run_scraping_process()

        if summary:
            print("\n✅ Scraping completed successfully!")
            print(f"📊 Summary:")
            print(f"   - Total links: {summary['total_links']}")
            print(f"   - Total images: {summary['total_images']}")
            print(f"   - Total references: {summary['total_references']}")
            print(f"   - Articles processed: {summary['unique_articles']}")
            print(f"\n📁 Files created:")
            print(f"   - links.csv")
            print(f"   - images.csv")
            print(f"   - references.csv")
            print(f"   - scraper.log")
        else:
            print("❌ Scraping failed. Check scraper.log for details.")

    except Exception as e:
        logger.error(f"Main execution failed: {e}")
        print(f"❌ Error: {e}")
        print("Check scraper.log for detailed error information.")

if __name__ == "__main__":
    main()