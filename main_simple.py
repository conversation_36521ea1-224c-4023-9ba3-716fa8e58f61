#!/usr/bin/env python3
"""
Simple and concise UI Charlotte articles scraper
"""

import time
import pandas as pd
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
import random
import re

# Configuration
BASE_URL = "https://ui.charlotte.edu"
ARTICLES_PAGE_URL = f"{BASE_URL}/articles/"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# User agents for rotation
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0'
]

def make_request(url):
    """Make a request with random user agent and delay"""
    headers = {'User-Agent': random.choice(USER_AGENTS)}
    time.sleep(random.uniform(1.0, 3.0))  # Random delay
    
    response = requests.get(url, headers=headers, timeout=30)
    response.raise_for_status()
    return response

def get_all_article_urls():
    """Get all article URLs from all pages"""
    logger.info("Starting article URL collection...")
    
    article_urls = set()
    current_url = ARTICLES_PAGE_URL
    page_count = 0
    
    while current_url and page_count < 200:  # Safety limit
        page_count += 1
        logger.info(f"Scraping page {page_count}: {current_url}")
        
        try:
            response = make_request(current_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find article links using container structure
            article_containers = soup.find_all('div', class_='listing-item-title')
            page_articles = set()
            
            for container in article_containers:
                link = container.find('a', href=True)
                if link:
                    href = link.get('href', '')
                    title = link.get_text(strip=True)
                    
                    if href and title:
                        full_url = urljoin(BASE_URL, href)
                        if full_url not in page_articles:
                            article_urls.add(full_url)
                            page_articles.add(full_url)
            
            if not page_articles:
                logger.warning(f"No article links found on page {page_count}")
                break
            
            logger.info(f"Found {len(page_articles)} articles on page {page_count}")
            
            # Find next page link
            next_link = soup.select_one('a.next.page-numbers')
            if next_link and next_link.get('href'):
                current_url = urljoin(BASE_URL, next_link['href'])
            else:
                logger.info("No more pages found")
                break
                
        except Exception as e:
            logger.error(f"Error scraping page {page_count}: {e}")
            break
    
    logger.info(f"Found {len(article_urls)} total articles")
    return list(article_urls)

def extract_references_from_section(article_url, heading, content_area):
    """Extract references from a section starting with a heading"""
    references = []

    # Look for content after the heading
    next_element = heading.find_next_sibling()

    while next_element:
        if next_element.name in ['ul', 'ol']:
            # Found a list - extract each item as a reference
            for item in next_element.find_all('li'):
                ref_text = item.get_text(strip=True)
                ref_link = item.find('a', href=True)
                link_url = urljoin(BASE_URL, ref_link['href']) if ref_link else 'No Link Found'

                if ref_text:
                    references.append({
                        'Article URL': article_url,
                        'Reference Text': ref_text,
                        'Link URL': link_url
                    })
            break

        elif next_element.name == 'p':
            # Found a paragraph - might contain references
            para_text = next_element.get_text(strip=True)
            if para_text:
                para_links = next_element.find_all('a', href=True)
                if para_links:
                    for link in para_links:
                        references.append({
                            'Article URL': article_url,
                            'Reference Text': para_text,
                            'Link URL': urljoin(BASE_URL, link['href'])
                        })
                else:
                    references.append({
                        'Article URL': article_url,
                        'Reference Text': para_text,
                        'Link URL': 'No Link Found'
                    })

        elif next_element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            # Hit another heading - stop looking
            break

        next_element = next_element.find_next_sibling()

    return references

def scrape_article_content(article_url):
    """Scrape links, images, and references from an article"""
    logger.debug(f"Scraping article: {article_url}")
    
    try:
        response = make_request(article_url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find the main content area
        content_area = soup.find('div', class_='page-body')
        if not content_area:
            content_area = soup.find('section', id='main') or soup.find('div', class_='page')
            if not content_area:
                logger.warning(f"No content area found for {article_url}")
                return [], [], []
        
        # Extract links
        links_data = []
        for link in content_area.find_all('a', href=True):
            href = link.get('href', '').strip()
            if href and not href.startswith('#') and not href.startswith('javascript:'):
                link_text = link.get_text(strip=True) or href
                full_url = urljoin(BASE_URL, href)
                links_data.append({
                    'Article URL': article_url,
                    'Link Name': link_text,
                    'Link URL': full_url
                })
        
        # Extract images
        images_data = []
        for img in content_area.find_all('img'):
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if src:
                full_url = urljoin(BASE_URL, src)
                images_data.append({
                    'Article URL': article_url,
                    'Image URL': full_url
                })
        
        # Extract references (improved)
        references_data = []

        # Method 1: Look for reference sections
        reference_keywords = ['references', 'citations', 'sources', 'bibliography', 'works cited']

        for keyword in reference_keywords:
            # Find headings that contain reference keywords
            headings = content_area.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'b'])

            for heading in headings:
                heading_text = heading.get_text(strip=True).lower()
                if keyword in heading_text:
                    # Found a references section, now extract the references
                    references_data.extend(extract_references_from_section(article_url, heading, content_area))
                    break

            if references_data:  # If we found references, stop looking
                break

        # Method 2: Look for numbered/bulleted reference lists even without clear headings
        if not references_data:
            reference_lists = content_area.find_all(['ol', 'ul'])
            for ref_list in reference_lists:
                list_items = ref_list.find_all('li')
                if len(list_items) >= 2:  # At least 2 items to consider it a reference list
                    # Check if items look like references (contain years, authors, etc.)
                    sample_text = ' '.join([li.get_text() for li in list_items[:3]])
                    if re.search(r'\b(19|20)\d{2}\b', sample_text):  # Contains years
                        for item in list_items:
                            ref_text = item.get_text(strip=True)
                            ref_link = item.find('a', href=True)
                            link_url = urljoin(BASE_URL, ref_link['href']) if ref_link else 'No Link Found'

                            if ref_text:
                                references_data.append({
                                    'Article URL': article_url,
                                    'Reference Text': ref_text,
                                    'Link URL': link_url
                                })
                        break

        # Method 3: Look for inline citations with actual reference text
        if not references_data:
            # Find paragraphs that might contain references
            paragraphs = content_area.find_all('p')
            for para in paragraphs:
                para_text = para.get_text(strip=True)
                # Look for patterns that suggest references (author names, years, etc.)
                if re.search(r'\b[A-Z][a-z]+,?\s+[A-Z]\.?\s*\(?(19|20)\d{2}\)?', para_text):
                    # This paragraph contains what looks like a citation
                    links_in_para = para.find_all('a', href=True)
                    if links_in_para:
                        for link in links_in_para:
                            link_text = link.get_text(strip=True)
                            if len(link_text) > 10:  # Substantial link text
                                references_data.append({
                                    'Article URL': article_url,
                                    'Reference Text': para_text,
                                    'Link URL': urljoin(BASE_URL, link['href'])
                                })
                    else:
                        # No links but looks like a reference
                        references_data.append({
                            'Article URL': article_url,
                            'Reference Text': para_text,
                            'Link URL': 'No Link Found'
                        })
        
        logger.debug(f"Extracted {len(links_data)} links, {len(images_data)} images, {len(references_data)} references")
        return links_data, images_data, references_data
        
    except Exception as e:
        logger.error(f"Error scraping {article_url}: {e}")
        return [], [], []

def main():
    """Main function to run the scraping process"""
    logger.info("Starting UI Charlotte articles scraping process...")
    
    try:
        # Step 1: Get all article URLs
        logger.info("Step 1: Collecting article URLs...")
        article_urls = get_all_article_urls()
        
        if not article_urls:
            logger.error("No article URLs found. Exiting.")
            return
        
        logger.info(f"Found {len(article_urls)} articles to scrape")
        
        # Step 2: Scrape each article
        logger.info("Step 2: Scraping article content...")
        all_links = []
        all_images = []
        all_references = []
        failed_articles = []
        
        for i, url in enumerate(article_urls, 1):
            logger.info(f"Scraping article {i}/{len(article_urls)}: {url}")
            
            try:
                links, images, references = scrape_article_content(url)
                all_links.extend(links)
                all_images.extend(images)
                all_references.extend(references)
                
                # Log progress every 10 articles
                if i % 10 == 0:
                    logger.info(f"Progress: {i}/{len(article_urls)} articles processed. "
                              f"Total: {len(all_links)} links, {len(all_images)} images, {len(all_references)} references")
                    
            except Exception as e:
                logger.error(f"Failed to scrape article {url}: {e}")
                failed_articles.append(url)
        
        # Step 3: Export data to CSV
        logger.info("Step 3: Exporting data to CSV files...")
        pd.DataFrame(all_links).to_csv('links.csv', index=False)
        pd.DataFrame(all_images).to_csv('images.csv', index=False)
        pd.DataFrame(all_references).to_csv('references.csv', index=False)
        
        # Final summary
        logger.info("Scraping process completed!")
        logger.info(f"Articles processed: {len(article_urls) - len(failed_articles)}/{len(article_urls)}")
        logger.info(f"Failed articles: {len(failed_articles)}")
        logger.info(f"Total links extracted: {len(all_links)}")
        logger.info(f"Total images extracted: {len(all_images)}")
        logger.info(f"Total references extracted: {len(all_references)}")
        
        print(f"\nScraping completed successfully!")
        print(f"Summary:")
        print(f"   - Total links: {len(all_links)}")
        print(f"   - Total images: {len(all_images)}")
        print(f"   - Total references: {len(all_references)}")
        print(f"   - Articles processed: {len(article_urls) - len(failed_articles)}")
        print(f"\nFiles created:")
        print(f"   - links.csv")
        print(f"   - images.csv")
        print(f"   - references.csv")
        print(f"   - scraper.log")
        
        if failed_articles:
            logger.warning("Failed articles:")
            for url in failed_articles:
                logger.warning(f"  - {url}")
        
    except Exception as e:
        logger.error(f"Scraping process failed: {e}")
        print(f"Error: {e}")
        print("Check scraper.log for detailed error information.")

if __name__ == "__main__":
    main()
