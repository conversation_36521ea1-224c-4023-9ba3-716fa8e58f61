2025-08-12 10:31:11,890 - INFO - Testing full scraper with limited articles...
2025-08-12 10:31:11,890 - INFO - Getting article URLs...
2025-08-12 10:31:11,891 - INFO - Starting article URL collection...
2025-08-12 10:31:11,891 - INFO - Scraping page 1: https://ui.charlotte.edu/articles/
2025-08-12 10:31:12,583 - INFO - Found 8 articles on page 1
2025-08-12 10:31:12,587 - INFO - Scraping page 2: https://ui.charlotte.edu/articles/page/2/
2025-08-12 10:31:13,647 - INFO - Found 10 articles on page 2
2025-08-12 10:31:13,652 - INFO - Scraping page 3: https://ui.charlotte.edu/articles/page/3/
2025-08-12 10:31:14,330 - INFO - Found 7 articles on page 3
2025-08-12 10:31:14,333 - INFO - Scraping page 4: https://ui.charlotte.edu/articles/page/4/
2025-08-12 10:31:15,290 - INFO - Found 1 articles on page 4
2025-08-12 10:31:15,294 - INFO - Scraping page 5: https://ui.charlotte.edu/articles/page/5/
2025-08-12 10:31:16,478 - WARNING - No article links found on page 5
2025-08-12 10:31:16,479 - INFO - Successfully collected 26 URLs using requests
2025-08-12 10:31:16,479 - INFO - Found 26 total articles
2025-08-12 10:31:16,480 - INFO - Testing with first 3 articles:
2025-08-12 10:31:16,480 - INFO -   1. https://ui.charlotte.edu/2025/01/28/who-comprises-the-remote-workforce-of-mecklenburg-county/
2025-08-12 10:31:16,480 - INFO -   2. https://ui.charlotte.edu/2024/06/20/new-report-finds-women-and-girls-in-charlotte-need-safety-and-support/
2025-08-12 10:31:16,480 - INFO -   3. https://ui.charlotte.edu/2024/06/20/understanding-corporate-landlords-decoding-a-recent-housing-phenomenon/
2025-08-12 10:31:16,481 - INFO - 
Scraping article 1/3: https://ui.charlotte.edu/2025/01/28/who-comprises-the-remote-workforce-of-mecklenburg-county/
2025-08-12 10:31:17,445 - INFO - 
Scraping article 2/3: https://ui.charlotte.edu/2024/06/20/new-report-finds-women-and-girls-in-charlotte-need-safety-and-support/
2025-08-12 10:31:18,108 - INFO - 
Scraping article 3/3: https://ui.charlotte.edu/2024/06/20/understanding-corporate-landlords-decoding-a-recent-housing-phenomenon/
2025-08-12 10:31:19,062 - INFO - 
Exporting test data...
2025-08-12 10:31:19,086 - INFO - Exported 20 links to test_links.csv
2025-08-12 10:31:19,086 - INFO - Exported 6 images to test_images.csv
2025-08-12 10:31:19,087 - INFO - Exported 36 references to test_references.csv
2025-08-12 10:31:19,090 - INFO -    - Articles processed: 3
2025-08-12 10:31:19,090 - INFO -    - Total links: 20
2025-08-12 10:31:19,090 - INFO -    - Total images: 6
2025-08-12 10:31:19,090 - INFO -    - Total references: 36
2025-08-12 10:31:19,093 - INFO - The full scraper should work. You can now run: python main.py
