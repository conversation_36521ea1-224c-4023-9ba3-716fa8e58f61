#!/usr/bin/env python3
"""
Test the full scraper with a limited number of articles
"""

import sys
import os

# Add the current directory to the path so we can import from main.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import UIArticlesScraper, ScrapingConfig
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_limited_scraping():
    """Test the full scraper with just a few articles"""
    
    logger.info("Testing full scraper with limited articles...")
    
    # Create a custom config for testing
    config = ScrapingConfig()
    config.request_delay_min = 0.5  # Faster for testing
    config.request_delay_max = 1.0
    
    # Create scraper
    scraper = UIArticlesScraper(config)
    
    try:
        # Step 1: Get article URLs
        logger.info("Getting article URLs...")
        article_urls = scraper.url_collector.get_all_article_urls()
        
        if not article_urls:
            logger.error("No articles found!")
            return False
        
        logger.info(f"Found {len(article_urls)} total articles")
        
        # Step 2: Test with just the first 2 articles (to save time)
        test_urls = article_urls[:2]
        logger.info(f"Testing with first {len(test_urls)} articles:")
        
        for i, url in enumerate(test_urls, 1):
            logger.info(f"  {i}. {url}")
        
        # Step 3: Scrape the test articles
        for i, url in enumerate(test_urls, 1):
            logger.info(f"\nScraping article {i}/{len(test_urls)}: {url}")
            
            try:
                links, images, references = scraper.content_scraper.scrape_article_details(url)
                scraper.data_manager.add_article_data(links, images, references)
                
                logger.info(f"  ✅ Success: {len(links)} links, {len(images)} images, {len(references)} references")
                
            except Exception as e:
                logger.error(f"  ❌ Failed: {e}")
        
        # Step 4: Export test data
        logger.info("\nExporting test data...")
        scraper.data_manager.export_to_csv(
            'test_links.csv',
            'test_images.csv', 
            'test_references.csv'
        )
        
        # Step 5: Show summary
        summary = scraper.data_manager.get_summary()
        logger.info(f"\n📊 Test Summary:")
        logger.info(f"   - Articles processed: {summary['unique_articles']}")
        logger.info(f"   - Total links: {summary['total_links']}")
        logger.info(f"   - Total images: {summary['total_images']}")
        logger.info(f"   - Total references: {summary['total_references']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False
    finally:
        scraper.cleanup()

if __name__ == "__main__":
    success = test_limited_scraping()
    
    if success:
        logger.info("\n✅ Limited scraping test completed successfully!")
        logger.info("The full scraper should work. You can now run: python main.py")
    else:
        logger.info("\n❌ Test failed. Check the logs above for issues.")
