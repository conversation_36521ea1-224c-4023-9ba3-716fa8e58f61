#!/usr/bin/env python3
"""
Test the improved reference extraction on a single article
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_simple import scrape_article_content
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_reference_extraction():
    """Test reference extraction on the article that had 36 references"""
    
    # This article had good references in our previous test
    test_url = "https://ui.charlotte.edu/2024/06/20/understanding-corporate-landlords-decoding-a-recent-housing-phenomenon/"
    
    logger.info(f"Testing reference extraction on: {test_url}")
    
    try:
        links, images, references = scrape_article_content(test_url)
        
        logger.info(f"Results:")
        logger.info(f"  Links: {len(links)}")
        logger.info(f"  Images: {len(images)}")
        logger.info(f"  References: {len(references)}")
        
        if references:
            logger.info(f"\nFirst 5 references:")
            for i, ref in enumerate(references[:5], 1):
                logger.info(f"  {i}. Text: {ref['Reference Text'][:100]}...")
                logger.info(f"     Link: {ref['Link URL']}")
                logger.info("")
        else:
            logger.info("No references found!")
            
        return len(references) > 0
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_reference_extraction()
    
    if success:
        logger.info("✅ Reference extraction test completed!")
    else:
        logger.info("❌ Reference extraction test failed!")
