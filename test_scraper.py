#!/usr/bin/env python3
"""
Test script to validate the scraping approach with a single article
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_single_article():
    """Test scraping a single article to validate our approach"""
    
    # Test URL - using the example from the task description
    test_url = "https://ui.charlotte.edu/2024/06/20/understanding-corporate-landlords-who-are-they/"
    base_url = "https://ui.charlotte.edu"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        logger.info(f"Testing article: {test_url}")
        
        # Make request
        response = requests.get(test_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Content length: {len(response.content)}")
        
        # Parse content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Debug: Let's see what content areas we can find
        logger.info("Debugging content area selectors...")

        content_selectors = [
            'div.entry-content',
            '.entry-content',
            '.post-content',
            '.article-content',
            '.content',
            'main',
            'article',
            '.post-body'
        ]

        content_area = None
        for selector in content_selectors:
            elements = soup.select(selector)
            logger.info(f"  Selector '{selector}': {len(elements)} elements")
            if elements and not content_area:
                content_area = elements[0]
                logger.info(f"  Using first element from '{selector}'")

        if not content_area:
            logger.error("No content area found with any selector!")
            # Let's see what the page structure looks like
            logger.info("Page structure debug:")
            body = soup.find('body')
            if body:
                # Find divs with class attributes that might contain content
                divs_with_classes = body.find_all('div', class_=True)
                for div in divs_with_classes:
                    classes = ' '.join(div.get('class', []))
                    text_content = div.get_text(strip=True)
                    if len(text_content) > 100:  # Only show divs with substantial content
                        logger.info(f"  <div class='{classes}'> - {len(text_content)} chars: {text_content[:100]}...")

                # Also check for any element with id
                elements_with_id = body.find_all(id=True)
                for elem in elements_with_id:
                    elem_id = elem.get('id')
                    text_content = elem.get_text(strip=True)
                    if len(text_content) > 100:
                        logger.info(f"  <{elem.name} id='{elem_id}'> - {len(text_content)} chars: {text_content[:100]}...")
            return False

        logger.info("Content area found successfully")
        
        # Test link extraction
        links = content_area.find_all('a', href=True)
        logger.info(f"Found {len(links)} links")
        
        for i, link in enumerate(links[:5]):  # Show first 5 links
            href = link.get('href')
            text = link.get_text(strip=True)
            full_url = urljoin(base_url, href)
            logger.info(f"  Link {i+1}: {text[:50]}... -> {full_url}")
        
        # Test image extraction
        images = content_area.find_all('img')
        logger.info(f"Found {len(images)} images")
        
        for i, img in enumerate(images[:3]):  # Show first 3 images
            src = img.get('src') or img.get('data-src')
            if src:
                full_url = urljoin(base_url, src)
                logger.info(f"  Image {i+1}: {full_url}")
        
        # Test reference extraction
        # Look for common reference patterns
        text_content = content_area.get_text().lower()
        has_references = any(keyword in text_content for keyword in ['reference', 'citation', 'source'])
        logger.info(f"Potential references section found: {has_references}")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

def test_articles_page():
    """Test accessing the main articles page"""
    
    articles_url = "https://ui.charlotte.edu/articles/"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        logger.info(f"Testing articles page: {articles_url}")
        
        response = requests.get(articles_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        logger.info(f"Response status: {response.status_code}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Debug: Let's see what article-related elements we can find
        logger.info("Debugging article selectors...")

        # Try different selectors
        selectors_to_try = [
            'h2.entry-title a',
            '.entry-title a',
            'h2 a',
            'article h2 a',
            'article .entry-title a',
            '.post-title a',
            '.article-title a'
        ]

        for selector in selectors_to_try:
            elements = soup.select(selector)
            logger.info(f"  Selector '{selector}': {len(elements)} elements")
            if elements:
                for i, elem in enumerate(elements[:2]):
                    logger.info(f"    {i+1}: {elem.get_text(strip=True)[:50]}...")

        # Also check for any links that might be articles
        all_links = soup.find_all('a', href=True)
        article_like_links = [link for link in all_links if '/202' in link.get('href', '')]  # Links with years
        logger.info(f"Found {len(article_like_links)} links that look like articles (contain '/202')")

        # Filter out duplicate URLs and "Read more" links
        unique_articles = {}
        for link in article_like_links:
            href = link.get('href')
            title = link.get_text(strip=True)
            if href not in unique_articles and title.lower() != 'read more':
                unique_articles[href] = title

        logger.info(f"Unique article URLs: {len(unique_articles)}")
        for i, (href, title) in enumerate(list(unique_articles.items())[:5]):
            logger.info(f"  Article {i+1}: {title[:50]}... -> {href}")

        # Check for pagination
        next_selectors = ['a.next.page-numbers', '.next', '.page-numbers.next', 'a[rel="next"]']
        for selector in next_selectors:
            next_link = soup.select_one(selector)
            if next_link:
                logger.info(f"Next page found with '{selector}': {next_link.get('href')}")
                break
        else:
            logger.info("No next page link found with any selector")
        
        return True
        
    except Exception as e:
        logger.error(f"Articles page test failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting scraper validation tests...")
    
    # Test 1: Articles page access
    logger.info("\n=== Test 1: Articles Page Access ===")
    articles_test = test_articles_page()
    
    # Test 2: Single article scraping
    logger.info("\n=== Test 2: Single Article Scraping ===")
    article_test = test_single_article()
    
    # Summary
    logger.info("\n=== Test Summary ===")
    logger.info(f"Articles page test: {'PASS' if articles_test else 'FAIL'}")
    logger.info(f"Single article test: {'PASS' if article_test else 'FAIL'}")
    
    if articles_test and article_test:
        logger.info("✅ All tests passed! The scraping approach should work.")
    else:
        logger.info("❌ Some tests failed. Check the logs above for details.")
