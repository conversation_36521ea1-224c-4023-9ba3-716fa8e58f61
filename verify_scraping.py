#!/usr/bin/env python3
"""
Verification script to check the quality and completeness of scraped data
"""

import pandas as pd
import requests
from urllib.parse import urlparse
import logging
import random
from collections import Counter
import re

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_scraped_data():
    """Load all scraped CSV files"""
    try:
        links_df = pd.read_csv('links.csv')
        images_df = pd.read_csv('images.csv')
        references_df = pd.read_csv('references.csv')
        
        logger.info(f"Loaded data: {len(links_df)} links, {len(images_df)} images, {len(references_df)} references")
        return links_df, images_df, references_df
    except FileNotFoundError as e:
        logger.error(f"CSV file not found: {e}")
        return None, None, None

def verify_data_structure(links_df, images_df, references_df):
    """Verify the structure and completeness of the data"""
    logger.info("=== DATA STRUCTURE VERIFICATION ===")
    
    issues = []
    
    # Check required columns
    required_columns = {
        'links': ['Article URL', 'Link Name', 'Link URL'],
        'images': ['Article URL', 'Image URL'],
        'references': ['Article URL', 'Reference Text', 'Link URL']
    }
    
    for df_name, df in [('links', links_df), ('images', images_df), ('references', references_df)]:
        if df is None:
            issues.append(f"❌ {df_name}.csv is missing")
            continue
            
        missing_cols = set(required_columns[df_name]) - set(df.columns)
        if missing_cols:
            issues.append(f"❌ {df_name}.csv missing columns: {missing_cols}")
        else:
            logger.info(f"✅ {df_name}.csv has correct columns")
    
    # Check for empty data
    for df_name, df in [('links', links_df), ('images', images_df), ('references', references_df)]:
        if df is not None and len(df) == 0:
            issues.append(f"❌ {df_name}.csv is empty")
    
    return issues

def verify_article_coverage(links_df, images_df, references_df):
    """Verify that articles are properly covered across all data types"""
    logger.info("=== ARTICLE COVERAGE VERIFICATION ===")
    
    issues = []
    
    if any(df is None for df in [links_df, images_df, references_df]):
        return ["❌ Cannot verify coverage - missing data files"]
    
    # Get unique articles from each dataset
    articles_with_links = set(links_df['Article URL'].unique())
    articles_with_images = set(images_df['Article URL'].unique())
    articles_with_references = set(references_df['Article URL'].unique())
    
    all_articles = articles_with_links | articles_with_images | articles_with_references
    
    logger.info(f"📊 Coverage Summary:")
    logger.info(f"   Total unique articles: {len(all_articles)}")
    logger.info(f"   Articles with links: {len(articles_with_links)}")
    logger.info(f"   Articles with images: {len(articles_with_images)}")
    logger.info(f"   Articles with references: {len(articles_with_references)}")
    
    # Check for articles missing from each category
    missing_links = all_articles - articles_with_links
    missing_images = all_articles - articles_with_images
    missing_references = all_articles - articles_with_references
    
    if missing_links:
        issues.append(f"⚠️  {len(missing_links)} articles have no links extracted")
    if missing_images:
        issues.append(f"⚠️  {len(missing_images)} articles have no images extracted")
    if missing_references:
        issues.append(f"⚠️  {len(missing_references)} articles have no references extracted")
    
    # Check for reasonable distribution
    avg_links = len(links_df) / len(all_articles) if all_articles else 0
    avg_images = len(images_df) / len(all_articles) if all_articles else 0
    avg_references = len(references_df) / len(all_articles) if all_articles else 0
    
    logger.info(f"📈 Averages per article:")
    logger.info(f"   Links: {avg_links:.1f}")
    logger.info(f"   Images: {avg_images:.1f}")
    logger.info(f"   References: {avg_references:.1f}")
    
    if avg_links < 1:
        issues.append(f"⚠️  Very low average links per article ({avg_links:.1f})")
    if avg_images < 0.5:
        issues.append(f"⚠️  Very low average images per article ({avg_images:.1f})")
    
    return issues

def verify_url_quality(links_df, images_df):
    """Verify the quality of extracted URLs"""
    logger.info("=== URL QUALITY VERIFICATION ===")
    
    issues = []
    
    if links_df is None or images_df is None:
        return ["❌ Cannot verify URLs - missing data files"]
    
    # Check link URLs
    invalid_links = 0
    for url in links_df['Link URL'].dropna():
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            invalid_links += 1
    
    if invalid_links > 0:
        issues.append(f"❌ {invalid_links} invalid link URLs found")
    else:
        logger.info(f"✅ All {len(links_df)} link URLs are valid")
    
    # Check image URLs
    invalid_images = 0
    for url in images_df['Image URL'].dropna():
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            invalid_images += 1
    
    if invalid_images > 0:
        issues.append(f"❌ {invalid_images} invalid image URLs found")
    else:
        logger.info(f"✅ All {len(images_df)} image URLs are valid")
    
    # Check for suspicious patterns
    placeholder_images = images_df[images_df['Image URL'].str.contains('data:image/svg', na=False)]
    if len(placeholder_images) > 0:
        logger.info(f"ℹ️  Found {len(placeholder_images)} placeholder/lazy-load images (normal)")
    
    return issues

def verify_content_quality(links_df, references_df):
    """Verify the quality of extracted content"""
    logger.info("=== CONTENT QUALITY VERIFICATION ===")
    
    issues = []
    
    if links_df is None or references_df is None:
        return ["❌ Cannot verify content - missing data files"]
    
    # Check link names
    empty_link_names = links_df[links_df['Link Name'].str.strip() == ''].shape[0]
    if empty_link_names > 0:
        issues.append(f"⚠️  {empty_link_names} links have empty names")
    
    # Check reference text quality
    short_references = references_df[references_df['Reference Text'].str.len() < 20].shape[0]
    if short_references > 0:
        issues.append(f"⚠️  {short_references} references have very short text (<20 chars)")
    
    # Check for generic reference text
    generic_refs = references_df[references_df['Reference Text'].str.contains('Inline reference', na=False)].shape[0]
    if generic_refs > 0:
        issues.append(f"⚠️  {generic_refs} references are generic 'Inline reference' entries")
    
    logger.info(f"✅ Content quality checks completed")
    
    return issues

def sample_verification(links_df):
    """Verify a random sample of articles by checking if they're still accessible"""
    logger.info("=== SAMPLE VERIFICATION ===")
    
    if links_df is None:
        return ["❌ Cannot perform sample verification - missing links data"]
    
    issues = []
    
    # Get unique article URLs
    article_urls = links_df['Article URL'].unique()
    
    # Sample 5 random articles
    sample_size = min(5, len(article_urls))
    sample_urls = random.sample(list(article_urls), sample_size)
    
    logger.info(f"Testing accessibility of {sample_size} random articles...")
    
    accessible_count = 0
    for url in sample_urls:
        try:
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                accessible_count += 1
                logger.info(f"✅ {url} - Accessible")
            else:
                logger.warning(f"⚠️  {url} - Status {response.status_code}")
                issues.append(f"⚠️  Article returned status {response.status_code}: {url}")
        except Exception as e:
            logger.error(f"❌ {url} - Error: {e}")
            issues.append(f"❌ Article not accessible: {url}")
    
    logger.info(f"📊 Sample accessibility: {accessible_count}/{sample_size} articles accessible")
    
    return issues

def generate_summary_report(links_df, images_df, references_df):
    """Generate a comprehensive summary report"""
    logger.info("=== SUMMARY REPORT ===")
    
    if any(df is None for df in [links_df, images_df, references_df]):
        logger.error("Cannot generate summary - missing data files")
        return
    
    # Basic statistics
    total_articles = len(set(links_df['Article URL'].unique()) | 
                         set(images_df['Article URL'].unique()) | 
                         set(references_df['Article URL'].unique()))
    
    # Domain analysis for links
    external_domains = Counter()
    for url in links_df['Link URL'].dropna():
        domain = urlparse(url).netloc
        if domain and 'ui.charlotte.edu' not in domain:
            external_domains[domain] += 1
    
    logger.info(f"📊 SCRAPING SUMMARY:")
    logger.info(f"   Total articles processed: {total_articles}")
    logger.info(f"   Total links extracted: {len(links_df)}")
    logger.info(f"   Total images extracted: {len(images_df)}")
    logger.info(f"   Total references extracted: {len(references_df)}")
    logger.info(f"   Unique external domains: {len(external_domains)}")
    
    if external_domains:
        logger.info(f"🔗 Top external domains:")
        for domain, count in external_domains.most_common(5):
            logger.info(f"   {domain}: {count} links")

def main():
    """Main verification function"""
    logger.info("🔍 Starting scraping verification...")
    
    # Load data
    links_df, images_df, references_df = load_scraped_data()
    
    all_issues = []
    
    # Run all verification checks
    all_issues.extend(verify_data_structure(links_df, images_df, references_df))
    all_issues.extend(verify_article_coverage(links_df, images_df, references_df))
    all_issues.extend(verify_url_quality(links_df, images_df))
    all_issues.extend(verify_content_quality(links_df, references_df))
    all_issues.extend(sample_verification(links_df))
    
    # Generate summary
    generate_summary_report(links_df, images_df, references_df)
    
    # Final assessment
    logger.info("\n" + "="*50)
    if not all_issues:
        logger.info("🎉 VERIFICATION PASSED - No issues found!")
    else:
        logger.info(f"⚠️  VERIFICATION COMPLETED - {len(all_issues)} issues found:")
        for issue in all_issues:
            logger.info(f"   {issue}")
    
    logger.info("🔍 Verification complete!")

if __name__ == "__main__":
    main()
